import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface SignupEmailProps {
  first_name?: string;
  email?: string;
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : '';

export const SignupEmail = ({
  first_name = 'User',
  email = '<EMAIL>',
}: SignupEmailProps) => (
  <Html>
    <Head />
    <Preview>You have been invited to join the RetailBook portal.</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={logoSection}>
          <Img
            src={`${baseUrl}/retailbook-logo.png`}
            width="199"
            height="143"
            alt="RetailBook"
            style={logo}
          />
        </Section>
        
        <Section style={content}>
          <Heading style={h1}>Sign-in to activate your RetailBook Account</Heading>
          
          <Text style={textLeft}>
            <strong>Hi {first_name},</strong>
          </Text>
          
          <Text style={text}>
            You have been invited to join the RetailBook portal.
          </Text>
          
          <Text style={text}>
            Please sign-in to the{' '}
            <Link href="https://portal.retailbook.com" style={link}>
              RetailBook
            </Link>{' '}
            portal using your email {email} to activate your account.
          </Text>
          
          <Text style={text}>
            The first time you log-in you will need to follow steps to reset your password.
          </Text>
        </Section>

        <Section style={dividerSection}>
          <hr style={divider} />
        </Section>

        <Section style={footer}>
          <Button
            href="mailto:<EMAIL>"
            style={button}
          >
            Contact Us
          </Button>
        </Section>
      </Container>
    </Body>
  </Html>
);

export default SignupEmail;

const main = {
  backgroundColor: '#f3f3f3',
  fontFamily: '"DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '600px',
  backgroundColor: '#ffffff',
};

const logoSection = {
  backgroundColor: '#f2eefb',
  padding: '30px 0',
  textAlign: 'center' as const,
};

const logo = {
  margin: '0 auto',
};

const content = {
  padding: '50px 20px 10px 20px',
  textAlign: 'center' as const,
};

const h1 = {
  color: '#000000',
  fontSize: '28px',
  fontWeight: 'bold',
  margin: '0 0 20px',
  textAlign: 'center' as const,
};

const text = {
  color: '#000000',
  fontSize: '14px',
  lineHeight: '22px',
  margin: '0 0 10px',
  textAlign: 'center' as const,
};

const textLeft = {
  color: '#000000',
  fontSize: '14px',
  lineHeight: '22px',
  margin: '0 0 10px',
  textAlign: 'left' as const,
};

const link = {
  color: '#2C63E2',
  textDecoration: 'none',
};

const dividerSection = {
  padding: '0 30px 0 40px',
};

const divider = {
  border: 'none',
  borderTop: '1px solid #000000',
  margin: '30px 0',
  width: '100%',
};

const footer = {
  padding: '18px 30px 30px 40px',
  textAlign: 'center' as const,
};

const button = {
  backgroundColor: '#ebf7ff',
  border: '1px solid #ebf7ff',
  borderRadius: '6px',
  color: '#000000',
  fontSize: '14px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 18px',
  width: '210px',
};
