import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
  Row,
  Column,
} from '@react-email/components';
import * as React from 'react';

interface OfferEmailProps {
  user_full_name?: string;
  offer_name?: string;
  offer_message?: string;
  offer_message_pre?: string;
  offer_message_post?: string;
  entered_by?: string;
  entered_by_email?: string;
  entered_by_self?: boolean;
  intermediary?: string;
  inside_information?: boolean;
  offer_is_inline?: boolean;
  
  // Shareholding info
  has_shareholding_info?: boolean;
  offer_shareholding_notional_value?: string;
  offer_shareholding_notional_value_delta?: string;
  offer_shareholding_applications?: string;
  offer_shareholding_applications_delta?: string;
  offer_shareholding?: string;
  offer_shareholding_delta?: string;
  
  // Non-shareholding info
  has_non_shareholding_info?: boolean;
  offer_non_shareholding_notional_value?: string;
  offer_non_shareholding_notional_value_delta?: string;
  offer_non_shareholding_applications?: string;
  offer_non_shareholding_applications_delta?: string;
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : '';

export const OfferEmail = ({
  user_full_name = 'User',
  offer_name = 'Offer',
  offer_message = 'Offer notification',
  offer_message_pre = '',
  offer_message_post = '',
  entered_by = '',
  entered_by_email = '',
  entered_by_self = false,
  intermediary = '',
  inside_information = false,
  offer_is_inline = false,
  has_shareholding_info = false,
  offer_shareholding_notional_value = '',
  offer_shareholding_notional_value_delta = '',
  offer_shareholding_applications = '',
  offer_shareholding_applications_delta = '',
  offer_shareholding = '',
  offer_shareholding_delta = '',
  has_non_shareholding_info = false,
  offer_non_shareholding_notional_value = '',
  offer_non_shareholding_notional_value_delta = '',
  offer_non_shareholding_applications = '',
  offer_non_shareholding_applications_delta = '',
}: OfferEmailProps) => {
  
  const renderEnteredBy = () => {
    if (entered_by && !entered_by_self) {
      return ` by ${entered_by}${entered_by_email ? ` (${entered_by_email})` : ''}`;
    }
    return '';
  };

  const renderOfferMessage = () => {
    if (offer_is_inline) {
      return (
        <Text style={text}>
          {offer_message_pre} {inside_information ? 'an Offer' : <strong>{offer_name}</strong>} {offer_message_post}{renderEnteredBy()}
        </Text>
      );
    } else {
      return (
        <>
          <Text style={text}>
            {offer_message}{renderEnteredBy()}
          </Text>
          {!inside_information && (
            <Text style={text}>
              <strong>{offer_name}</strong>
            </Text>
          )}
        </>
      );
    }
  };

  return (
    <Html>
      <Head />
      <Preview>RetailBook offer notification</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoSection}>
            <Img
              src={`${baseUrl}/retailbook-logo.png`}
              width="199"
              height="143"
              alt="RetailBook"
              style={logo}
            />
          </Section>
          
          <Section style={content}>
            <Heading style={h1}>RetailBook Notification</Heading>
            
            <Text style={text}>
              <strong>Hi {user_full_name},</strong>
            </Text>
            
            {renderOfferMessage()}
            
            {intermediary && (
              <Text style={text}>
                <strong>{intermediary}</strong>
              </Text>
            )}

            {/* Shareholding Information Table */}
            {has_shareholding_info && (
              <Section style={tableSection}>
                <Heading style={tableHeading}>Existing Shareholders</Heading>
                <table style={table}>
                  <tbody>
                    <tr>
                      <td style={tableCellLeft}>New Cash Total</td>
                      <td style={tableCellRight}>
                        {offer_shareholding_notional_value}
                        {offer_shareholding_notional_value_delta && ` (${offer_shareholding_notional_value_delta})`}
                      </td>
                    </tr>
                    <tr>
                      <td style={tableCellLeft}>New Number of Applications</td>
                      <td style={tableCellRight}>
                        {offer_shareholding_applications}
                        {offer_shareholding_applications_delta && ` (${offer_shareholding_applications_delta})`}
                      </td>
                    </tr>
                    <tr>
                      <td style={tableCellLeft}>New Existing Shareholding</td>
                      <td style={tableCellRight}>
                        {offer_shareholding}
                        {offer_shareholding_delta && ` (${offer_shareholding_delta})`}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </Section>
            )}

            {/* Non-Shareholding Information Table */}
            {has_non_shareholding_info && (
              <Section style={tableSection}>
                <Heading style={tableHeading}>New Shareholders</Heading>
                <table style={table}>
                  <tbody>
                    <tr>
                      <td style={tableCellLeft}>New Cash Total</td>
                      <td style={tableCellRight}>
                        {offer_non_shareholding_notional_value}
                        {offer_non_shareholding_notional_value_delta && ` (${offer_non_shareholding_notional_value_delta})`}
                      </td>
                    </tr>
                    <tr>
                      <td style={tableCellLeft}>New Number of Applications</td>
                      <td style={tableCellRight}>
                        {offer_non_shareholding_applications}
                        {offer_non_shareholding_applications_delta && ` (${offer_non_shareholding_applications_delta})`}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </Section>
            )}
            
            <Text style={text}>
              Please log on to{' '}
              <Link href="https://portal.retailbook.com" style={link}>
                RetailBook
              </Link>{' '}
              to view further information{inside_information ? ', this may be considered inside information.' : '.'}
            </Text>
          </Section>

          <Section style={dividerSection}>
            <hr style={divider} />
          </Section>

          <Section style={footer}>
            <Heading style={h2}>Do you have further questions?</Heading>
            
            <Button
              href="mailto:<EMAIL>"
              style={button}
            >
              Contact Us
            </Button>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default OfferEmail;

const main = {
  backgroundColor: '#f3f3f3',
  fontFamily: '"DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '600px',
  backgroundColor: '#ffffff',
};

const logoSection = {
  backgroundColor: '#f2eefb',
  padding: '30px 0',
  textAlign: 'center' as const,
};

const logo = {
  margin: '0 auto',
};

const content = {
  padding: '50px 20px 10px 20px',
  textAlign: 'center' as const,
};

const h1 = {
  color: '#000000',
  fontSize: '28px',
  fontWeight: 'bold',
  margin: '0 0 20px',
  textAlign: 'center' as const,
};

const h2 = {
  color: '#000000',
  fontSize: '28px',
  fontWeight: 'normal',
  margin: '18px 0',
  textAlign: 'center' as const,
};

const text = {
  color: '#000000',
  fontSize: '14px',
  lineHeight: '22px',
  margin: '0 0 10px',
  textAlign: 'center' as const,
};

const link = {
  color: '#2C63E2',
  textDecoration: 'none',
};

const tableSection = {
  margin: '20px 0',
};

const tableHeading = {
  color: '#000000',
  fontSize: '16px',
  fontWeight: 'bold',
  margin: '10px 0',
  textAlign: 'center' as const,
};

const table = {
  width: '100%',
  margin: '0 auto',
  maxWidth: '400px',
};

const tableCellLeft = {
  padding: '5px',
  fontSize: '14px',
  lineHeight: '22px',
  textAlign: 'left' as const,
  color: '#000000',
};

const tableCellRight = {
  padding: '5px',
  fontSize: '14px',
  lineHeight: '22px',
  textAlign: 'left' as const,
  color: '#000000',
};

const dividerSection = {
  padding: '0 30px 0 40px',
};

const divider = {
  border: 'none',
  borderTop: '1px solid #000000',
  margin: '30px 0',
  width: '100%',
};

const footer = {
  padding: '18px 30px 30px 40px',
  textAlign: 'center' as const,
};

const button = {
  backgroundColor: '#ebf7ff',
  border: '1px solid #ebf7ff',
  borderRadius: '6px',
  color: '#000000',
  fontSize: '14px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 18px',
  width: '210px',
};
