# RetailBook React Email Templates

Modern React Email templates for the RetailBook platform, converted from the original SendGrid HTML templates.

## Overview

This package contains three main email templates:

1. **Notification Template** - General notifications about outstanding portal items
2. **Offer Template** - Offer-related notifications with shareholding information
3. **Signup Template** - User account activation and invitation emails

## Installation

```bash
npm install
```

## Development

```bash
# Start the development server
npm run dev

# Build templates
npm run build

# Preview templates
npm run preview
```

## Templates

### 1. Notification Email

**Purpose**: Notify users about outstanding notifications on the RetailBook portal.

**Props**:
- `first_name` (string, optional): User's first name (default: "User")
- `subject` (string, optional): Email subject (default: "RetailBook Notification")

**Example**:
```tsx
import { NotificationEmail } from './emails';

<NotificationEmail
  first_name="<PERSON>"
  subject="RetailBook Notification"
/>
```

### 2. Offer Email

**Purpose**: Send offer-related notifications including applications, allocations, and shareholding information.

**Props**:
- `user_full_name` (string): User's full name
- `offer_name` (string): Name of the offer
- `offer_message` (string): Main message content
- `offer_message_pre` (string): Pre-offer message part (for inline offers)
- `offer_message_post` (string): Post-offer message part (for inline offers)
- `entered_by` (string): Who entered the information
- `entered_by_email` (string): Email of who entered the information
- `entered_by_self` (boolean): Whether user entered it themselves
- `intermediary` (string): Intermediary/participant name
- `inside_information` (boolean): Whether this is inside information
- `offer_is_inline` (boolean): Whether to display offer inline

**Shareholding Information**:
- `has_shareholding_info` (boolean): Show existing shareholders section
- `offer_shareholding_notional_value` (string): Cash total for existing shareholders
- `offer_shareholding_notional_value_delta` (string): Change in cash total
- `offer_shareholding_applications` (string): Number of applications
- `offer_shareholding_applications_delta` (string): Change in applications
- `offer_shareholding` (string): Existing shareholding amount
- `offer_shareholding_delta` (string): Change in shareholding

**Non-Shareholding Information**:
- `has_non_shareholding_info` (boolean): Show new shareholders section
- `offer_non_shareholding_notional_value` (string): Cash total for new shareholders
- `offer_non_shareholding_notional_value_delta` (string): Change in cash total
- `offer_non_shareholding_applications` (string): Number of applications
- `offer_non_shareholding_applications_delta` (string): Change in applications

**Example**:
```tsx
import { OfferEmail } from './emails';

<OfferEmail
  user_full_name="John Smith"
  offer_name="TechCorp IPO"
  offer_message="Applications have opened for TechCorp IPO on RetailBook."
  has_shareholding_info={true}
  offer_shareholding_notional_value="£1,250,000"
  offer_shareholding_applications="15"
  offer_shareholding="2.5%"
/>
```

### 3. Signup Email

**Purpose**: Welcome new users and provide account activation instructions.

**Props**:
- `first_name` (string, optional): User's first name (default: "User")
- `email` (string, optional): User's email address (default: "<EMAIL>")

**Example**:
```tsx
import { SignupEmail } from './emails';

<SignupEmail
  first_name="Alice"
  email="<EMAIL>"
/>
```

## Design System

The templates use the RetailBook design system with the following key elements:

**Colors**:
- Primary Blue: `#2C63E2` (bluetiful-step-0)
- Background: `#f3f3f3`
- Logo Background: `#f2eefb`
- Text: `#000000`
- Button Background: `#ebf7ff`

**Typography**:
- Font Family: "DM Sans" (matching the main application)
- Heading: 28px, bold
- Body Text: 14px, line-height 22px

**Layout**:
- Max Width: 600px
- Responsive design
- Consistent spacing and padding

## Examples

See the `examples/` directory for complete usage examples of each template with various configurations.

## Integration

These templates can be integrated with your email service provider (SendGrid, etc.) by rendering them to HTML:

```tsx
import { render } from '@react-email/render';
import { NotificationEmail } from './emails';

const html = render(<NotificationEmail first_name="John" />);
// Send html via your email service
```

## Brand Consistency

The templates maintain consistency with the RetailBook brand:
- Uses the same color palette as the main application
- Matches typography and spacing
- Includes RetailBook logo and branding
- Consistent "Contact Us" call-to-action
