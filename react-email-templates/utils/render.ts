import { render } from '@react-email/render';
import { NotificationEmail, OfferEmail, SignupEmail } from '../emails';

// Type definitions for template data
export interface NotificationData {
  first_name?: string;
  subject?: string;
}

export interface OfferData {
  user_full_name?: string;
  offer_name?: string;
  offer_message?: string;
  offer_message_pre?: string;
  offer_message_post?: string;
  entered_by?: string;
  entered_by_email?: string;
  entered_by_self?: boolean;
  intermediary?: string;
  inside_information?: boolean;
  offer_is_inline?: boolean;
  has_shareholding_info?: boolean;
  offer_shareholding_notional_value?: string;
  offer_shareholding_notional_value_delta?: string;
  offer_shareholding_applications?: string;
  offer_shareholding_applications_delta?: string;
  offer_shareholding?: string;
  offer_shareholding_delta?: string;
  has_non_shareholding_info?: boolean;
  offer_non_shareholding_notional_value?: string;
  offer_non_shareholding_notional_value_delta?: string;
  offer_non_shareholding_applications?: string;
  offer_non_shareholding_applications_delta?: string;
}

export interface SignupData {
  first_name?: string;
  email?: string;
}

// Template rendering functions
export const renderNotificationEmail = (data: NotificationData): string => {
  return render(NotificationEmail(data));
};

export const renderOfferEmail = (data: OfferData): string => {
  return render(OfferEmail(data));
};

export const renderSignupEmail = (data: SignupData): string => {
  return render(SignupEmail(data));
};

// Generic template renderer
export const renderTemplate = (
  templateType: 'notification' | 'offer' | 'signup',
  data: NotificationData | OfferData | SignupData
): string => {
  switch (templateType) {
    case 'notification':
      return renderNotificationEmail(data as NotificationData);
    case 'offer':
      return renderOfferEmail(data as OfferData);
    case 'signup':
      return renderSignupEmail(data as SignupData);
    default:
      throw new Error(`Unknown template type: ${templateType}`);
  }
};

// Example usage for integration with SendGrid or other email services
export const prepareEmailForSending = (
  templateType: 'notification' | 'offer' | 'signup',
  data: NotificationData | OfferData | SignupData,
  to: string,
  subject: string
) => {
  const html = renderTemplate(templateType, data);
  
  return {
    to,
    subject,
    html,
    // You can also generate plain text version if needed
    // text: htmlToText(html)
  };
};
