import { OfferEmail } from '../emails';

// Example usage of the Offer email template with shareholding information
export const OfferExampleWithShareholding = () => (
  <OfferEmail
    user_full_name="<PERSON>"
    offer_name="TechCorp IPO"
    offer_message="Applications have opened for TechCorp IPO on RetailBook."
    entered_by="<PERSON>"
    entered_by_email="<EMAIL>"
    entered_by_self={false}
    intermediary="Goldman Sachs"
    inside_information={false}
    offer_is_inline={false}
    has_shareholding_info={true}
    offer_shareholding_notional_value="£1,250,000"
    offer_shareholding_notional_value_delta="+£250,000"
    offer_shareholding_applications="15"
    offer_shareholding_applications_delta="+3"
    offer_shareholding="2.5%"
    offer_shareholding_delta="+0.5%"
    has_non_shareholding_info={true}
    offer_non_shareholding_notional_value="£750,000"
    offer_non_shareholding_notional_value_delta="+£150,000"
    offer_non_shareholding_applications="8"
    offer_non_shareholding_applications_delta="+2"
  />
);

// Example usage of the Offer email template for simple notification
export const OfferExampleSimple = () => (
  <OfferEmail
    user_full_name="<PERSON>"
    offer_name="RetailCorp Rights Issue"
    offer_message="Your allocation for RetailCorp Rights Issue is available on RetailBook."
    inside_information={false}
    offer_is_inline={false}
    has_shareholding_info={false}
    has_non_shareholding_info={false}
  />
);

// Example usage for inside information
export const OfferExampleInsideInfo = () => (
  <OfferEmail
    user_full_name="Michael Brown"
    offer_message="Applications have closed for an Offer on RetailBook."
    inside_information={true}
    offer_is_inline={true}
    offer_message_pre="Applications have closed for"
    offer_message_post="on RetailBook."
    has_shareholding_info={false}
    has_non_shareholding_info={false}
  />
);

export default OfferExampleWithShareholding;
